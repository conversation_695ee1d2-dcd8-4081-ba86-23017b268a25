// OutOfStockCard.tsx
// Stokta olmayan ürünler için card component

import React from 'react';
import { Card, CardContent } from '../common/Card';
import { OutOfStockItem } from '../../types/dashboard.types';
import { cn } from '../../lib/utils';
import { Clock } from 'lucide-react';

export interface OutOfStockCardProps {
  item: OutOfStockItem;
  className?: string;
}

export const OutOfStockCard: React.FC<OutOfStockCardProps> = ({
  item,
  className
}) => {
  return (
    <Card className={cn('backdrop-blur-sm border-0 shadow-md hover:shadow-xl transition-all duration-300', className)}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          {/* Item Image */}
          <div className="flex-shrink-0">
            <div className="relative">
              <img
                src={item.image}
                alt={item.name}
                className="w-14 h-14 rounded-xl object-cover opacity-50 shadow-md"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/default-dish.jpg';
                }}
              />
              {/* Out of stock overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-red-500/30 to-transparent rounded-xl flex items-center justify-center">
                <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg shadow-red-200/50">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
              </div>
            </div>
          </div>

          {/* Item Info */}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold text-slate-900 truncate">
              {item.name}
            </h4>
            <div className="flex items-center mt-1 text-sm text-slate-500 font-medium">
              <div className="w-6 h-6 bg-slate-100 rounded-lg flex items-center justify-center mr-2">
                <Clock className="w-3 h-3 text-slate-400" />
              </div>
              <span>Tahmini: {item.availableAt}</span>
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex-shrink-0">
            <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-200/50">
              Stokta Yok
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
