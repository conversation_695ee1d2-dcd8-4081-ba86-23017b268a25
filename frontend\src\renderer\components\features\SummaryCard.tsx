// SummaryCard.tsx
// Dashboard özet kartları için component

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../common/Card';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { cn } from '../../lib/utils';

export interface SummaryCardProps {
  title: string;
  value: number | string;
  change: string;
  icon?: React.ReactNode;
  isLoading?: boolean;
  className?: string;
}

export const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  change,
  icon,
  isLoading = false,
  className
}) => {
  const isPositiveChange = change.startsWith('+');
  const isNegativeChange = change.startsWith('-');

  return (
    <Card className={cn('relative overflow-hidden backdrop-blur-sm border-0 shadow-lg', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 p-6 xl:p-8">
        <CardTitle className="text-sm xl:text-base font-semibold text-slate-700">
          {title}
        </CardTitle>
        {icon && (
          <div className="h-5 w-5 xl:h-6 xl:w-6 text-slate-500">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent className="p-6 xl:p-8 pt-0">
        {isLoading ? (
          <div className="flex items-center justify-center h-16 xl:h-20">
            <LoadingSpinner size="sm" />
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-2xl xl:text-3xl font-bold text-slate-900">
              {typeof value === 'number' && title.toLowerCase().includes('kazanç')
                ? `₺${value.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}`
                : value
              }
            </div>
            <div className={cn(
              'text-sm flex items-center font-medium',
              isPositiveChange && 'text-emerald-600',
              isNegativeChange && 'text-red-500',
              !isPositiveChange && !isNegativeChange && 'text-slate-500'
            )}>
              <span className={cn(
                'mr-2 w-5 h-5 rounded-full flex items-center justify-center text-xs',
                isPositiveChange && 'bg-emerald-100 text-emerald-600',
                isNegativeChange && 'bg-red-100 text-red-500',
                !isPositiveChange && !isNegativeChange && 'bg-slate-100 text-slate-500'
              )}>
                {isPositiveChange && '↗'}
                {isNegativeChange && '↘'}
                {!isPositiveChange && !isNegativeChange && '→'}
              </span>
              <span className="hidden sm:inline">{change} önceki güne göre</span>
              <span className="sm:hidden">{change}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
