// Dashboard.tsx
// Ana Dashboard sayfası

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  TrendingUp,
  Clock,
  Users,
  Search,
  Package,
  AlertTriangle,
  ShoppingCart
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/common/Card';
import { EmptyState } from '../components/common/EmptyState';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { SummaryCard } from '../components/features/SummaryCard';
import { PopularDishCard } from '../components/features/PopularDishCard';
import { OutOfStockCard } from '../components/features/OutOfStockCard';
import { OrderCard } from '../components/features/OrderCard';
import { BottomNavigation } from '../components/features/BottomNavigation';

import { useDashboardStore } from '../store/dashboardStore';
import { useAuthStore } from '../store/authStore';
import { apiService } from '../services/api.service';
import { OrderStatus } from '../types/dashboard.types';
import { cn } from '../lib/utils';

export const Dashboard: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<OrderStatus>('in_progress');
  const [currentNavTab, setCurrentNavTab] = useState('home');
  
  const { user } = useAuthStore();
  const {
    summary,
    popularDishes,
    outOfStockItems,
    orders,
    filters,
    loading,
    error,
    setSummary,
    setPopularDishes,
    setOutOfStockItems,
    setOrders,
    setFilters,
    setLoading,
    setError,
    clearError
  } = useDashboardStore();

  // Dashboard Summary Query
  const { data: summaryData, isLoading: summaryLoading } = useQuery({
    queryKey: ['dashboard-summary'],
    queryFn: async () => {
      const response = await apiService.getDashboardSummary();
      if (response.success && response.data) {
        setSummary(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Özet veriler alınamadı');
    },
    refetchInterval: 30000, // 30 saniyede bir güncelle
  });

  // Popular Dishes Query
  const { data: dishesData, isLoading: dishesLoading } = useQuery({
    queryKey: ['popular-dishes'],
    queryFn: async () => {
      const response = await apiService.getPopularDishes(10);
      if (response.success && response.data) {
        setPopularDishes(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Popüler yemekler alınamadı');
    },
    refetchInterval: 60000, // 1 dakikada bir güncelle
  });

  // Out of Stock Query
  const { data: stockData, isLoading: stockLoading } = useQuery({
    queryKey: ['out-of-stock'],
    queryFn: async () => {
      const response = await apiService.getOutOfStockItems();
      if (response.success && response.data) {
        setOutOfStockItems(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Stok bilgileri alınamadı');
    },
    refetchInterval: 120000, // 2 dakikada bir güncelle
  });

  // Orders Query
  const { data: ordersData, isLoading: ordersLoading, refetch: refetchOrders } = useQuery({
    queryKey: ['dashboard-orders', activeTab],
    queryFn: async () => {
      const response = await apiService.getDashboardOrders({
        status: activeTab,
        limit: 50
      });
      if (response.success && response.data) {
        setOrders(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Siparişler alınamadı');
    },
    refetchInterval: 15000, // 15 saniyede bir güncelle
  });

  // Filter orders based on search query
  const filteredOrders = orders.filter(order => 
    order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.tableNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    order.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleTabChange = (tab: OrderStatus) => {
    setActiveTab(tab);
    setFilters({ orderStatus: tab });
  };

  const handleCreateNewOrder = () => {
    // TODO: Navigate to order creation page
    console.log('Create new order');
  };

  const handlePayNow = (orderId: string) => {
    // TODO: Navigate to payment page
    console.log('Pay now for order:', orderId);
  };

  const handleViewOrder = (orderId: string) => {
    // TODO: Navigate to order details page
    console.log('View order:', orderId);
  };

  const handleNavTabChange = (tabId: string) => {
    setCurrentNavTab(tabId);
    // TODO: Navigate to different pages based on tabId
    console.log('Navigate to:', tabId);
  };

  return (
    <div className="h-screen bg-gray-50 overflow-hidden flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="h-full p-4 xl:p-6 pb-20 space-y-4 xl:space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl xl:text-2xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-sm xl:text-base text-gray-600">
                Hoş geldin, {user?.firstName} {user?.lastName}
              </p>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 xl:gap-6">
          <SummaryCard
            title="Toplam Kazanç"
            value={summary?.totalEarning || 0}
            change={summary?.totalEarningChange || '0%'}
            icon={<TrendingUp className="w-4 h-4" />}
            isLoading={summaryLoading}
          />
          <SummaryCard
            title="Devam Eden Siparişler"
            value={summary?.inProgressOrders || 0}
            change={summary?.inProgressOrdersChange || '0%'}
            icon={<Clock className="w-4 h-4" />}
            isLoading={summaryLoading}
          />
          <SummaryCard
            title="Ödeme Bekleyen"
            value={summary?.waitingList || 0}
            change={summary?.waitingListChange || '0%'}
            icon={<Users className="w-4 h-4" />}
            isLoading={summaryLoading}
          />
        </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 xl:gap-6 flex-1 min-h-0">
            {/* Orders Section */}
            <div className="lg:col-span-2 space-y-4 xl:space-y-6 flex flex-col min-h-0">
              {/* Search and Tabs */}
              <Card className="flex-1 flex flex-col min-h-0">
                <CardHeader className="pb-4">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <CardTitle className="text-base xl:text-lg">Siparişler</CardTitle>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder="Sipariş ara..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full sm:w-64 pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  {/* Tabs */}
                  <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                    <button
                      onClick={() => handleTabChange('in_progress')}
                      className={cn(
                        'flex-1 py-2 px-2 xl:px-4 text-xs xl:text-sm font-medium rounded-md transition-colors',
                        activeTab === 'in_progress'
                          ? 'bg-white text-blue-600 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      )}
                    >
                      <span className="hidden sm:inline">Devam Ediyor</span>
                      <span className="sm:hidden">Devam</span>
                      <span className="ml-1">({summary?.inProgressOrders || 0})</span>
                    </button>
                    <button
                      onClick={() => handleTabChange('waiting_for_payment')}
                      className={cn(
                        'flex-1 py-2 px-2 xl:px-4 text-xs xl:text-sm font-medium rounded-md transition-colors',
                        activeTab === 'waiting_for_payment'
                          ? 'bg-white text-blue-600 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      )}
                    >
                      <span className="hidden sm:inline">Ödeme Bekliyor</span>
                      <span className="sm:hidden">Ödeme</span>
                      <span className="ml-1">({summary?.waitingList || 0})</span>
                    </button>
                  </div>
                </CardHeader>

                <CardContent className="flex-1 overflow-y-auto">
                  {ordersLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <LoadingSpinner />
                    </div>
                  ) : filteredOrders.length === 0 ? (
                    <EmptyState
                      icon={<ShoppingCart className="w-12 h-12" />}
                      title="Sipariş Bulunamadı"
                      description={
                        searchQuery
                          ? "Arama kriterlerinize uygun sipariş bulunamadı."
                          : activeTab === 'in_progress'
                            ? "Şu anda devam eden sipariş bulunmuyor."
                            : "Ödeme bekleyen sipariş bulunmuyor."
                      }
                      actionLabel={!searchQuery ? "Yeni Sipariş Oluştur" : undefined}
                      onAction={!searchQuery ? handleCreateNewOrder : undefined}
                    />
                  ) : (
                    <div className="space-y-3">
                      {filteredOrders.map((order) => (
                        <OrderCard
                          key={order.id}
                          order={order}
                          status={activeTab}
                          onPayNow={handlePayNow}
                          onViewOrder={handleViewOrder}
                        />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-4 xl:space-y-6 flex flex-col min-h-0">
              {/* Popular Dishes */}
              <Card className="flex-1 flex flex-col min-h-0">
                <CardHeader className="pb-4">
                  <CardTitle className="text-base xl:text-lg flex items-center">
                    <Package className="w-4 xl:w-5 h-4 xl:h-5 mr-2" />
                    Popüler Yemekler
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto">
                  {dishesLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <LoadingSpinner size="sm" />
                    </div>
                  ) : popularDishes.length === 0 ? (
                    <p className="text-sm text-gray-500 text-center py-4">
                      Henüz popüler yemek verisi yok
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {popularDishes.slice(0, 5).map((dish, index) => (
                        <PopularDishCard
                          key={dish.id}
                          dish={dish}
                          rank={index + 1}
                        />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Out of Stock */}
              <Card className="flex-1 flex flex-col min-h-0">
                <CardHeader className="pb-4">
                  <CardTitle className="text-base xl:text-lg flex items-center">
                    <AlertTriangle className="w-4 xl:w-5 h-4 xl:h-5 mr-2" />
                    Stokta Olmayan
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto">
                  {stockLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <LoadingSpinner size="sm" />
                    </div>
                  ) : outOfStockItems.length === 0 ? (
                    <p className="text-sm text-gray-500 text-center py-4">
                      Tüm ürünler stokta mevcut
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {outOfStockItems.slice(0, 5).map((item) => (
                        <OutOfStockCard
                          key={item.id}
                          item={item}
                        />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation
        activeTab={currentNavTab}
        onTabChange={handleNavTabChange}
        onCreateOrder={handleCreateNewOrder}
      />
    </div>
  );
};
