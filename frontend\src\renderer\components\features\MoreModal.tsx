// MoreModal.tsx
// "Daha Fazla" modal component'i

import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { 
  X, 
  Store, 
  BarChart3, 
  Users, 
  Wifi, 
  Bell, 
  Eye, 
  Globe, 
  HelpCircle, 
  Shield, 
  Info,
  Settings,
  LogOut
} from 'lucide-react';
import { Button } from '../common/Button';
import { cn } from '../../lib/utils';

export interface MoreModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MoreMenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  category: 'tools' | 'settings' | 'help';
  onClick?: () => void;
}

export const MoreModal: React.FC<MoreModalProps> = ({ isOpen, onClose }) => {
  const menuItems: MoreMenuItem[] = [
    // Tools
    {
      id: 'outlet-info',
      title: 'Şube Bilgileri',
      icon: <Store className="w-5 h-5" />,
      category: 'tools',
      onClick: () => console.log('Outlet Information')
    },
    {
      id: 'reports',
      title: '<PERSON><PERSON>',
      icon: <BarChart3 className="w-5 h-5" />,
      category: 'tools',
      onClick: () => console.log('Report Summary')
    },
    {
      id: 'customers',
      title: 'Müşteri Verileri',
      icon: <Users className="w-5 h-5" />,
      category: 'tools',
      onClick: () => console.log('Customer Data')
    },
    
    // Settings
    {
      id: 'devices',
      title: 'Bağlı Cihazlar',
      icon: <Wifi className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Connected Devices')
    },
    {
      id: 'notifications',
      title: 'Bildirimler',
      icon: <Bell className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Notifications')
    },
    {
      id: 'appearance',
      title: 'Görünüm',
      icon: <Eye className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Appearance')
    },
    {
      id: 'language',
      title: 'Dil',
      icon: <Globe className="w-5 h-5" />,
      category: 'settings',
      onClick: () => console.log('Language')
    },
    
    // Help
    {
      id: 'help-center',
      title: 'Yardım Merkezi',
      icon: <HelpCircle className="w-5 h-5" />,
      category: 'help',
      onClick: () => console.log('Help Center')
    },
    {
      id: 'privacy',
      title: 'Gizlilik Politikası',
      icon: <Shield className="w-5 h-5" />,
      category: 'help',
      onClick: () => console.log('Privacy Policy')
    },
    {
      id: 'app-info',
      title: 'Uygulama Bilgileri',
      icon: <Info className="w-5 h-5" />,
      category: 'help',
      onClick: () => console.log('App Information')
    }
  ];

  const categories = [
    { id: 'tools', title: 'Araçlar', items: menuItems.filter(item => item.category === 'tools') },
    { id: 'settings', title: 'Ayarlar', items: menuItems.filter(item => item.category === 'settings') },
    { id: 'help', title: 'Yardım', items: menuItems.filter(item => item.category === 'help') }
  ];

  const handleItemClick = (item: MoreMenuItem) => {
    item.onClick?.();
    onClose();
  };

  const handleLogout = () => {
    // TODO: Implement logout functionality
    console.log('Logout');
    onClose();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-1/2 top-1/2 z-50 w-full max-w-md -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] max-h-[90vh] overflow-hidden">
          
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <Dialog.Title className="text-lg font-semibold text-gray-900">
              Daha Fazla
            </Dialog.Title>
            <Dialog.Close asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <X className="w-4 h-4" />
              </Button>
            </Dialog.Close>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
            <div className="p-6 space-y-6">
              {categories.map((category) => (
                <div key={category.id}>
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                    {category.title}
                  </h3>
                  <div className="grid grid-cols-3 gap-4">
                    {category.items.map((item) => (
                      <button
                        key={item.id}
                        onClick={() => handleItemClick(item)}
                        className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
                      >
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-200 transition-colors duration-200">
                          <div className="text-blue-600">
                            {item.icon}
                          </div>
                        </div>
                        <span className="text-xs font-medium text-gray-700 text-center leading-tight">
                          {item.title}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <Button
              variant="outline"
              onClick={handleLogout}
              className="w-full flex items-center justify-center space-x-2 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
            >
              <LogOut className="w-4 h-4" />
              <span>Çıkış Yap</span>
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
